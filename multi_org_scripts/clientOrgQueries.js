const lodash = require("lodash");
const csv = require("csvtojson");

function replaceNullStringWithNull(input) {
  return input.replace(/'NULL'/g, "null");
}

const script = async () => {
  const clientData = await csv().fromFile("clients_prod.csv");

  const insertQueries = [];

  clientData.forEach(function (data) {
    const {
      id,
      organisationId,
      customerId,
      type,
      organisationClientRoleId,
      leapOnboardingCompleted,
      requiredActionStatus,
    } = data;
    if (leapOnboardingCompleted === "True") {
      const columns = [
        "clientId",
        "organisationId",
        "customerId",
        "organisationClientRoleId",
        "status",
        "type",
        "requiredActionStatus",
      ].join(`", "`);

      const values = [
        id,
        organisationId,
        customerId,
        organisationClientRoleId,
        "ACTIVE",
        type,
        requiredActionStatus,
      ]
        .map((value) =>
          typeof value === "string" ? `'${value.replace(/'/g, "''")}'` : value
        )
        .join(", ");
      insertQueries.push(
        `INSERT INTO "ClientOrganisation" ("${columns}") VALUES (${replaceNullStringWithNull(
          values
        )});`
      );
    }
  });
  console.log(insertQueries.join("\r\n"));
};

script();
