const lodash = require("lodash");
const csv = require("csvtojson");

const script = async () => {
  const clientOrgData = await csv().fromFile("client_org_prod.csv");

  const updateQueries = [];

  clientOrgData.forEach(function (data) {
    const { id, clientId } = data;
    updateQueries.push(
      `UPDATE "ClientTrait" SET "clientOrganisationId"=${id} WHERE "clientId"=${clientId};`
    );
  });
  console.log(updateQueries.join("\r\n"));
};

script();
