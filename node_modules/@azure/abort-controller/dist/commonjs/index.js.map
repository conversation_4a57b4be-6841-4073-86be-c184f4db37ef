{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAMlC,iDAA6C;AAApC,2GAAA,UAAU,OAAA", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\ndeclare global {\n  interface Event {}\n}\n\nexport { AbortError } from \"./AbortError.js\";\nexport { AbortSignalLike } from \"./AbortSignalLike.js\";\n"]}