const { createReport } = require("docx-templates");
const fs = require("fs");

// const test = async () => {
//   const template = fs.readFileSync("template.docx");
//   const buffer = await createReport({
//     template,
//     data: {
//       name: "<PERSON>",
//       surname: "<PERSON>see<PERSON>",
//     },
//     cmdDelimiter: ["+++", "+++"],
//   });
//   fs.writeFileSync("report.pdf", buffer);
// };

// test();

// var convertapi = require("convertapi")("T0Fy4nPdQpcRgbR0");
// convertapi
//   .convert(
//     "pdf",
//     {
//       File: "/path/to/my_file.docx",
//     },
//     "docx"
//   )
//   .then(function (result) {
//     result.saveFiles("/path/to/dir");
//   });

var convertapi = require("convertapi")("T0Fy4nPdQpcRgbR0");
convertapi
  .convert("pdf", {
    File: "./quote-template.docx",
    JsonPayload: [
      {
        Name: "Quote Number",
        Value: 7458,
        Type: "integer",
      },
      {
        Name: "Issue Date",
        Value: "2024-07-30",
        Type: "datetime",
      },
      {
        Name: "Due Date",
        Value: "2024-08-30",
        Type: "datetime",
      },
      {
        Name: "ArchitecturalWork",
        Value: "7 999.99",
        Type: "string",
      },
      {
        Name: "InteriorWork",
        Value: "6 000.00",
        Type: "string",
      },
      {
        Name: "GrandTotal",
        Value: "13 999.99",
        Type: "string",
      },
      {
        Name: "Customer",
        Value: "ConvertAPI",
        Type: "string",
      },
    ],
  })
  .then(function (result) {
    result.saveFiles("./");
  });
