// This Node.js example calls the Docmosis web service to create a PDF document
// from the default template (samples/WelcomeTemplate.docx).
// To run:
//  1) plug your Docmosis access key into the accessKey variable
//  2) Make sure your you have the correct region specified in the hostname variable
//
// Copyright Docmosis 2019
//

const https = require("https");
const fs = require("fs");

// Set your access key here. This is visible in your cloud account in the Docmosis Web Site.
// It is your key to accessing your service - keep it private and safe.
const accessKey =
  "***************************************************************";

// RESTful service host. Set your region here.
// End-point in the USA
// const hostname = "us1.dws4.docmosis.com";
// End-point in the EU
//const hostname = "eu1.dws4.docmosis.com";
// End-point in AUS
const hostname = "au1.dws4.docmosis.com";

// make sure the access key above has been set
if (accessKey == "XXXXX") {
  console.log("Please set your access key in this example");
  process.exit();
}

// The template to use
// NOTE that it has to be defined in your account with the same name specified here
const template = "sampless/WelcomeTemplate.docx"; // Change with your actual template name

// The output file name and extension
const output = "/tmp/myWelcome.docx";

const test = async () => {
  // The data to use for generating the document
  const data = {
    title: "Welcome to Docmosis in the Cloud",
    messages: [
      { msg: "This cloud experience is better than I thought." },
      { msg: "The sun is shining." },
      { msg: "Right, now back to work. YOYOYOY" },
    ],
  };

  const postData = new URLSearchParams({
    accessKey: accessKey,
    templateName: template,
    outputName: output,
    data: JSON.stringify(data),
  }).toString();

  const options = {
    hostname: hostname,
    port: 443,
    path: "/api/render",
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
      "Content-Length": Buffer.byteLength(postData),
    },
  };

  const req = await https.request(options, (res) => {
    switch (res.statusCode) {
      case 200:
        var file = fs.createWriteStream(output);

        // feed response into file
        res.pipe(file);
        file.on("finish", () => {
          file.close();
          console.log(output, "created");
        });
        break;
      default:
        // 4XX errors - client errors - something needs to be corrected in the request
        // 5XX errors - server side errors - possibly worth a retry

        // show error response (details)
        console.log("Error response:", res.statusCode, res.statusMessage);
        var response = "";
        res.on("data", (data) => {
          response += data;
        });
        res.on("end", () => {
          console.log(response);
        });
    }
  });

  req.on("error", (e) => {
    console.error("Request error:", JSON.stringify(e, null, 4));
  });

  // write data to request body
  req.write(postData);
  req.end();
};

test();
