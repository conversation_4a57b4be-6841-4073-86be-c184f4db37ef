from google.cloud import storage
from vertexai.vision_models import Image
from vertexai.preview.generative_models import GenerativeModel, Part, Content
import os
import mimetypes
import vertexai

# --- CONFIG ---
PROJECT_ID = "australia-post-retail-mobility"  # <-- Replace this
LOCATION = "australia-southeast2"              # or the region your Vertex AI is in
BUCKET_NAME = "post_img_bucket_test"           # <-- Replace this
LOCAL_IMAGE_PATH = "./sample.jpeg"
DESTINATION_BLOB_NAME = "uploads/image.jpg"

# --- Initialize Vertex AI client ---
vertexai.init(project=PROJECT_ID, location=LOCATION)

# --- Upload to GCS ---
def upload_to_gcs(bucket_name, source_file_name, destination_blob_name):
    storage_client = storage.Client(project=PROJECT_ID)  # Pass project explicitly
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(destination_blob_name)
    blob.upload_from_filename(source_file_name)
    print(f"✅ Uploaded to gs://{bucket_name}/{destination_blob_name}")
    return f"gs://{bucket_name}/{destination_blob_name}"

# --- Analyze with Gemini (GCS) ---
def analyze_image_with_gemini(gcs_uri):
    image = Image.load_from_uri(gcs_uri)
    model = GenerativeModel("gemini-1.5-pro-vision")
    prompt = "Describe the content of this image."
    response = model.generate_content([prompt, image])
    print("\n🧠 Gemini Response:\n", response.text)

# --- Analyze local image with Gemini ---
def analyze_local_image(image_path):
    # Load image bytes and detect MIME type
    mime_type, _ = mimetypes.guess_type(image_path)
    with open(image_path, "rb") as f:
        image_bytes = f.read()

    image_part = Part.from_data(data=image_bytes, mime_type=mime_type or "image/jpeg")
    prompt_part = Part.from_text("Describe the content of this image.")

    # Create Content object with both parts
    user_content = Content(role="user", parts=[prompt_part, image_part])

    # Run Gemini model
    model = GenerativeModel("gemini-1.5-pro-vision")
    response = model.generate_content(user_content)

    print("\n🧠 Gemini Response:\n", response.text)

# --- Main ---
if __name__ == "__main__":
    # gcs_uri = upload_to_gcs(BUCKET_NAME, LOCAL_IMAGE_PATH, DESTINATION_BLOB_NAME)
    # analyze_image_with_gemini(gcs_uri)
    analyze_local_image(LOCAL_IMAGE_PATH)

