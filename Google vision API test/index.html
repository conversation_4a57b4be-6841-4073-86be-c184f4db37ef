<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Address OCR with Vision API</title>
    <style>
      body {
        font-family: sans-serif;
        margin: 2rem;
      }
      input[type="file"] {
        margin-bottom: 1rem;
      }
      pre {
        background: #f4f4f4;
        padding: 1rem;
        overflow-x: auto;
      }
    </style>
  </head>
  <body>
    <h1>Upload Image to Extract Address</h1>
    <input type="file" id="imageInput" accept="image/*" />
    <button onclick="handleUpload()">Upload and Process</button>
    <h2>Extracted JSON</h2>
    <pre id="output">Waiting for upload...</pre>

    <script>
      const API_KEY = "AIzaSyCZqlAAGaYJg8C2C9uzThHMKCLTYYT_PEA"; // Replace with your actual key

      async function handleUpload() {
        const fileInput = document.getElementById("imageInput");
        const output = document.getElementById("output");

        if (!fileInput.files.length) {
          output.textContent = "Please select an image file first.";
          return;
        }

        const file = fileInput.files[0];
        const base64Image = await toBase64(file);

        const response = await fetch(
          `https://vision.googleapis.com/v1/images:annotate?key=${API_KEY}`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              requests: [
                {
                  image: { content: base64Image },
                  features: [
                    {
                      type: "DOCUMENT_TEXT_DETECTION",
                    },
                  ],
                },
              ],
            }),
          }
        );

        const data = await response.json();
        const rawOcrText = data.responses[0]?.fullTextAnnotation?.text;
        console.log("OCR Result:", rawOcrText);
        if (!rawOcrText) {
          output.textContent = "No text found in the image.";
        } else {
          //   output.textContent = rawOcrText;

          output.textContent = data.responses[0]?.fullTextAnnotation
            ? JSON.stringify(data.responses[0]?.fullTextAnnotation, null, 2)
            : "No text found in the image.";
          // output.textContent = JSON.stringify(
          //   extractAddressJson(rawOcrText),
          //   null,
          //   2
          // );
        }
      }

      function toBase64(file) {
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.readAsDataURL(file);
          reader.onload = () => resolve(reader.result.split(",")[1]);
          reader.onerror = (error) => reject(error);
        });
      }

      function extractAddressJson(ocrText) {
        const lines = ocrText
          .split("\n")
          .map((line) => line.trim())
          .filter(Boolean);

        const result = {
          name: null,
          street: null,
          city: null,
          state: null,
          zip: null,
        };

        for (let line of lines) {
          // Extract name
          if (
            !result.name &&
            line.match(/Mr\\.?|Mrs\\.?|Ms\\.?|Dr\\.?|Leigh|Carasso/)
          ) {
            result.name = line;
          }

          // Extract street
          if (!result.street && line.match(/^\\d+\\s+\\w+/)) {
            result.street = line;
          }

          // Extract city, state, ZIP
          const cityStateZip = line.match(
            /^(.*?),?\\s+([A-Z]{2})\\s+(\\d{5})$/
          );
          if (cityStateZip) {
            result.city = cityStateZip[1];
            result.state = cityStateZip[2];
            result.zip = cityStateZip[3];
          }
        }

        return result;
      }
    </script>
  </body>
</html>
