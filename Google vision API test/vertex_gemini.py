import vertexai
from vertexai.generative_models import GenerativeModel, Part, Image
import os
import mimetypes  # For reliable MIME type detection
import time       # For measuring performance

def describe_image_with_gemini_local_path(project_id: str, location: str, image_path: str):
    """
    Sends a local image to Vertex AI's Gemini Pro model
    and gets a description.

    Args:
        project_id (str): Your Google Cloud project ID.
        location (str): The Google Cloud region (e.g., "us-central1").
        image_path (str): The path to the local image file.
    """
    vertexai.init(project=project_id, location=location)

    multimodal_model = GenerativeModel("gemini-1.5-flash")  # Using Gemini 1.5 Pro

    try:
        with open(image_path, 'rb') as f:
            image_bytes = f.read()

        # Determine MIME type using mimetypes
        mime_type, _ = mimetypes.guess_type(image_path)
        if not mime_type:
            print(f"Warning: Could not determine image type for {image_path}. Defaulting to 'image/jpeg'.")
            mime_type = "image/jpeg"  # Fallback
        elif not mime_type.startswith("image/"):
            print(f"Error: Invalid image type for {image_path}.  Supported types are JPEG and PNG.  MIME type detected: {mime_type}")
            return  # Exit the function

        image_part = Part.from_data(data=image_bytes, mime_type=mime_type)

        # --- The Key Prompt for Address Extraction ---
        prompt = """
        I want you to act as an tool that analyses Australian Postal Addresses. 
        Break down addresses into the following format – Addressee name: Business name: PO: Level: Unit: Street number: Street name: Suburb: Postcode: State:.
        If some of the fields are not found in the address, please put NaN against the field.
        Analyze the image and identify all Australian postal addresses.
        For each address found, extract the components and provide them in a JSON array.
        Your response MUST be valid JSON and contain ONLY the JSON array. Do not include any other text or explanation.
        """

        contents = [image_part, prompt]

        print(f"Sending image {image_path} to Gemini Pro...")
        # --- Performance Measurement ---
        start_time = time.perf_counter() # More precise than time.time()

        response = multimodal_model.generate_content(contents)

        end_time = time.perf_counter()
        time_taken = end_time - start_time
        # --- End Performance Measurement ---

        print("\n--- Gemini Pro Description ---")
        print(response.text)

        print(f"\n--- Performance ---")
        print(f"Time taken for Gemini response: {time_taken:.4f} seconds")

    except Exception as e:
        print(f"An error occurred: {e}")


if __name__ == "__main__":
    # --- Configuration ---
    PROJECT_ID = "australia-post-retail-mobility"  # Replace with your Google Cloud Project ID
    REGION = "australia-southeast1"         # Using australia-southeast1

    if PROJECT_ID == "YOUR_PROJECT_ID":
        print("Please replace 'YOUR_PROJECT_ID' with your actual Google Cloud Project ID.")
        print("You can find it in the Google Cloud Console dashboard.")
        exit()

    # --- Option 1: Describe an image from a local file path ---
    local_image_filename = "image_12.png"  # Using your specified image file

    if os.path.exists(local_image_filename):
        print(f"\n--- Describing local image '{local_image_filename}' using Gemini Pro ---")
        describe_image_with_gemini_local_path(PROJECT_ID, REGION, local_image_filename)
    else:
        print(f"\nError: Local image '{local_image_filename}' not found in the current directory.")
        print("Please ensure 'image.jpg' is in the same folder as this script, or update the 'local_image_filename' variable.")

    # Example of a non-existent or private URI (uncomment to test error handling)
    # print("\n--- Testing error with a non-existent GCS URI ---")
    # describe_image_with_gemini_gcs_uri(PROJECT_ID, REGION, 'gs://your-non-existent-bucket/non-existent-image.jpg')