const lodash = require("lodash");
const csv = require("csvtojson");

const script = async () => {
  const clientTraits = await csv().fromFile("clientTrait.csv");
  const defaultQuestionsMapping = {
    2: 39562647,
    3: 39562645,
    4: 39562648,
    5: 39562650,
  };

  const defaultAnswersMapping = {
    747: 39563464,
    748: 39563465,
    749: 39563466,
    750: 39563467,
    751: 39563468,
    752: 39563469,
    753: 39563470,
    754: 39563452,
    755: 39563453,
    756: 39563454,
    757: 39563455,
    758: 39563456,
    759: 39563457,
    760: 39563471,
    761: 39563472,
    762: 39563473,
    763: 39563474,
    764: 39563475,
    765: 39563476,
    776: 39563483,
    777: 39563484,
    778: 39563485,
    779: 39563486,
    780: 39563487,
    781: 39563488,
    782: 39563489,
    783: 39563490,
    4131: 39563491,
  };

  const insertTaitQueries = [];

  clientTraits.forEach(function (clientTrait) {
    const { clientId, traitId, traitValueId } = clientTrait;
    const clientIdVal = parseFloat(clientId);
    const traitIdVal = parseFloat(traitId);
    const traitValueIdVal = parseFloat(traitValueId);
    if (
      // clientIdVal === 11623 &&
      // -----------------
      [2, 3, 4, 5].includes(traitIdVal)
    ) {
      if (
        defaultQuestionsMapping[traitIdVal] &&
        defaultAnswersMapping[traitValueIdVal]
      ) {
        insertTaitQueries.push(
          `INSERT INTO "ClientTrait" ("clientId", "traitId","traitValueId") VALUES (${clientIdVal},${defaultQuestionsMapping[traitIdVal]},${defaultAnswersMapping[traitValueIdVal]});`
        );
      }
    }
  });
  // console.log(insertTaitQueries.length);
  console.log(insertTaitQueries.join("\r\n"));
};

script();
