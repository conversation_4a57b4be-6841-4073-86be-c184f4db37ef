import requests
import time

USERS = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
]

PASSWORD = "Abcd@12cs"
BASE_URL = "https://api-dev.ennie.app"  # Replace with your actual API base
# BASE_URL = "http://localhost:5005"  # Replace with your actual API base

def login(username):
    try:
        res = requests.post(f"{BASE_URL}/api/auth/login", json={
            "username": username,
            "password": PASSWORD
        })
        res.raise_for_status()
        data = res.json()["data"]
        token = data["access_token"]
        profile_id = data["profiles"][0]["profile_id"]  # Assumes at least 1 profile
        # print(f"[{username}] Logged in. Profile ID: {profile_id}")
        return token, profile_id
    except Exception as e:
        print(f"[{username}] Login failed: {e}")
        return None, None

def ping_user(username, token):
    user_id = username.split("@")[0]
    headers = {"Authorization": f"Bearer {token}"}
    try:
        res = requests.get(f"{BASE_URL}/api/users/{user_id}/ping", headers=headers)
        res.raise_for_status()
        # print(f"[{username}] Ping successful: {res.status_code}")
    except Exception as e:
        print(f"[{username}] Ping failed: {e}")

def create_session(username, token, profile_id):
    headers = {"Authorization": f"Bearer {token}"}
    payload = {"profile_id": profile_id}
    try:
        res = requests.post(f"{BASE_URL}/api/sessions", json=payload, headers=headers)
        res.raise_for_status()
        session_id = res.json()["data"]["session_id"]
        print(f"[{username}] Session created. Session ID: {session_id}")
        return session_id
    except Exception as e:
        print(f"[{username}] Failed to create session: {e}")
        return None

def complete_intake(username, token, profile_id, session_id):
    headers = {"Authorization": f"Bearer {token}"}
    payload = {
    "profile_id": profile_id,
    "session_id": session_id,
    "conversation_status": "COMPLETED",
    "conversation_type": "INTAKE",
    "conversation": [
        {
            "message_type": "Audio",
            "content": "Hello, I'm Charlie Goldsmith. I'm here to help you get ready for your healing session. Can you tell me what's going on with you today?",
            "sender_type": "AI"
        },
        {
            "content": "I have a headache.",
            "message_type": "Audio",
            "sender_type": "Patient"
        },
        {
            "message_type": "Audio",
            "content": "Okay, I understand. Can you tell me a little bit more about your pain?  On a scale of zero to ten, with zero being no pain and ten being the worst pain you can imagine, how would you rate the severity of your pain right now? ",
            "sender_type": "AI"
        },
        {
            "message_type": "Audio",
            "content": "seven.",
            "sender_type": "Patient"
        },
        {
            "message_type": "Audio",
            "content": "Any other symptoms ?",
            "sender_type": "AI"
        },
        {
            "message_type": "Audio",
            "content": "No",
            "sender_type": "Patient"
        }
        ]
    }
    try:
        res = requests.post(f"{BASE_URL}/api/conversations/bulk", json=payload, headers=headers)
        print(f"[{username}] Completed intake: {res.status_code}")
    except Exception as e:
        print(f"[{username}] Failed to complete intake: {e}")

def run_tests():
    user_tokens = {}
    for username in USERS:
        token, profile_id = login(username)
        if not token or not profile_id:
            continue
        user_tokens[username] = token
        ping_user(username, token)
        session_id = create_session(username, token, profile_id)
        if not session_id:
            continue
        complete_intake(username, token, profile_id, session_id)
    return user_tokens

def ping_users_infinite(user_tokens):
    while True:
        for username, token in user_tokens.items():
            ping_user(username, token)
        time.sleep(30)

if __name__ == "__main__":
    user_tokens = run_tests()
    ping_users_infinite(user_tokens)

